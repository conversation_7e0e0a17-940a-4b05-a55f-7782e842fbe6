"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\r\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');\\r\\n\\r\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\r\\n\\r\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\r\\n\\r\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\r\\n\\r\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\r\\n\\r\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\r\\n\\r\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\r\\n\\r\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\r\\n\\r\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\r\\n\\r\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\r\\n\\r\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\r\\n\\r\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\r\\n\\r\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\r\\n\\r\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\r\\n\\r\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\r\\n\\r\\n/*\\nRemove the default font size and weight for headings.\\n*/\\r\\n\\r\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\r\\n\\r\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\r\\n\\r\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\r\\n\\r\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\r\\n\\r\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\r\\n\\r\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\r\\n\\r\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\r\\n\\r\\n/*\\nAdd the correct font size in all browsers.\\n*/\\r\\n\\r\\nsmall {\\n  font-size: 80%;\\n}\\r\\n\\r\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\r\\n\\r\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\r\\n\\r\\nsub {\\n  bottom: -0.25em;\\n}\\r\\n\\r\\nsup {\\n  top: -0.5em;\\n}\\r\\n\\r\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\r\\n\\r\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\r\\n\\r\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\r\\n\\r\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\r\\n\\r\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\r\\n\\r\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\r\\n\\r\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\r\\n\\r\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\r\\n\\r\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\r\\n\\r\\n:-moz-focusring {\\n  outline: auto;\\n}\\r\\n\\r\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\r\\n\\r\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\r\\n\\r\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\r\\n\\r\\nprogress {\\n  vertical-align: baseline;\\n}\\r\\n\\r\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\r\\n\\r\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\r\\n\\r\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\r\\n\\r\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\r\\n\\r\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\r\\n\\r\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\r\\n\\r\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\r\\n\\r\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\r\\n\\r\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\r\\n\\r\\nsummary {\\n  display: list-item;\\n}\\r\\n\\r\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\r\\n\\r\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\r\\n\\r\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\r\\n\\r\\nlegend {\\n  padding: 0;\\n}\\r\\n\\r\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\r\\n\\r\\n/*\\nReset default styling for dialogs.\\n*/\\r\\n\\r\\ndialog {\\n  padding: 0;\\n}\\r\\n\\r\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\r\\n\\r\\ntextarea {\\n  resize: vertical;\\n}\\r\\n\\r\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\r\\n\\r\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\r\\n\\r\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\r\\n\\r\\n/*\\nSet the default cursor for buttons.\\n*/\\r\\n\\r\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\r\\n\\r\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\r\\n\\r\\n:disabled {\\n  cursor: default;\\n}\\r\\n\\r\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\r\\n\\r\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\r\\n\\r\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\r\\n\\r\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\r\\n\\r\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\r\\n\\r\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n\\r\\n:root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --primary: 222.2 47.4% 11.2%;\\r\\n    --primary-foreground: 210 40% 98%;\\r\\n\\r\\n    --secondary: 210 40% 96.1%;\\r\\n    --secondary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --muted: 210 40% 96.1%;\\r\\n    --muted-foreground: 215.4 16.3% 46.9%;\\r\\n\\r\\n    --accent: 210 40% 96.1%;\\r\\n    --accent-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 214.3 31.8% 91.4%;\\r\\n    --input: 214.3 31.8% 91.4%;\\r\\n    --ring: 222.2 84% 4.9%;\\r\\n\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    --sidebar-background: 0 0% 98%;\\r\\n    --sidebar-foreground: 240 5.3% 26.1%;\\r\\n    --sidebar-primary: 240 5.9% 10%;\\r\\n    --sidebar-primary-foreground: 0 0% 98%;\\r\\n    --sidebar-accent: 240 4.8% 95.9%;\\r\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\r\\n    --sidebar-border: 220 13% 91%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n.dark {\\r\\n    --background: 222.2 84% 4.9%;\\r\\n    --foreground: 210 40% 98%;\\r\\n\\r\\n    --card: 222.2 84% 4.9%;\\r\\n    --card-foreground: 210 40% 98%;\\r\\n\\r\\n    --popover: 222.2 84% 4.9%;\\r\\n    --popover-foreground: 210 40% 98%;\\r\\n\\r\\n    --primary: 210 40% 98%;\\r\\n    --primary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --secondary: 217.2 32.6% 17.5%;\\r\\n    --secondary-foreground: 210 40% 98%;\\r\\n\\r\\n    --muted: 217.2 32.6% 17.5%;\\r\\n    --muted-foreground: 215 20.2% 65.1%;\\r\\n\\r\\n    --accent: 217.2 32.6% 17.5%;\\r\\n    --accent-foreground: 210 40% 98%;\\r\\n\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 217.2 32.6% 17.5%;\\r\\n    --input: 217.2 32.6% 17.5%;\\r\\n    --ring: 212.7 26.8% 83.9%;\\r\\n    --sidebar-background: 240 5.9% 10%;\\r\\n    --sidebar-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-primary: 224.3 76.3% 48%;\\r\\n    --sidebar-primary-foreground: 0 0% 100%;\\r\\n    --sidebar-accent: 240 3.7% 15.9%;\\r\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-border: 240 3.7% 15.9%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n*{\\n  border-color: hsl(var(--border));\\n}\\r\\n\\r\\nbody{\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\n}\\r\\n.sr-only{\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none{\\n  pointer-events: none;\\n}\\r\\n.pointer-events-auto{\\n  pointer-events: auto;\\n}\\r\\n.visible{\\n  visibility: visible;\\n}\\r\\n.invisible{\\n  visibility: hidden;\\n}\\r\\n.fixed{\\n  position: fixed;\\n}\\r\\n.absolute{\\n  position: absolute;\\n}\\r\\n.relative{\\n  position: relative;\\n}\\r\\n.inset-0{\\n  inset: 0px;\\n}\\r\\n.inset-x-0{\\n  left: 0px;\\n  right: 0px;\\n}\\r\\n.inset-y-0{\\n  top: 0px;\\n  bottom: 0px;\\n}\\r\\n.-bottom-12{\\n  bottom: -3rem;\\n}\\r\\n.-left-12{\\n  left: -3rem;\\n}\\r\\n.-right-12{\\n  right: -3rem;\\n}\\r\\n.-top-12{\\n  top: -3rem;\\n}\\r\\n.bottom-0{\\n  bottom: 0px;\\n}\\r\\n.left-0{\\n  left: 0px;\\n}\\r\\n.left-1{\\n  left: 0.25rem;\\n}\\r\\n.left-1\\\\/2{\\n  left: 50%;\\n}\\r\\n.left-2{\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\]{\\n  left: 50%;\\n}\\r\\n.right-0{\\n  right: 0px;\\n}\\r\\n.right-1{\\n  right: 0.25rem;\\n}\\r\\n.right-2{\\n  right: 0.5rem;\\n}\\r\\n.right-3{\\n  right: 0.75rem;\\n}\\r\\n.right-4{\\n  right: 1rem;\\n}\\r\\n.top-0{\\n  top: 0px;\\n}\\r\\n.top-1\\\\.5{\\n  top: 0.375rem;\\n}\\r\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\r\\n.top-2{\\n  top: 0.5rem;\\n}\\r\\n.top-3\\\\.5{\\n  top: 0.875rem;\\n}\\r\\n.top-4{\\n  top: 1rem;\\n}\\r\\n.top-\\\\[1px\\\\]{\\n  top: 1px;\\n}\\r\\n.top-\\\\[50\\\\%\\\\]{\\n  top: 50%;\\n}\\r\\n.top-\\\\[60\\\\%\\\\]{\\n  top: 60%;\\n}\\r\\n.top-full{\\n  top: 100%;\\n}\\r\\n.z-10{\\n  z-index: 10;\\n}\\r\\n.z-20{\\n  z-index: 20;\\n}\\r\\n.z-50{\\n  z-index: 50;\\n}\\r\\n.z-\\\\[100\\\\]{\\n  z-index: 100;\\n}\\r\\n.z-\\\\[1\\\\]{\\n  z-index: 1;\\n}\\r\\n.float-right{\\n  float: right;\\n}\\r\\n.float-left{\\n  float: left;\\n}\\r\\n.-mx-1{\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-2{\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-3\\\\.5{\\n  margin-left: 0.875rem;\\n  margin-right: 0.875rem;\\n}\\r\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-0\\\\.5{\\n  margin-top: 0.125rem;\\n  margin-bottom: 0.125rem;\\n}\\r\\n.my-1{\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.-ml-4{\\n  margin-left: -1rem;\\n}\\r\\n.-mt-4{\\n  margin-top: -1rem;\\n}\\r\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-10{\\n  margin-bottom: 2.5rem;\\n}\\r\\n.mb-12{\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16{\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2{\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-auto{\\n  margin-left: auto;\\n}\\r\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-3{\\n  margin-right: 0.75rem;\\n}\\r\\n.mt-1\\\\.5{\\n  margin-top: 0.375rem;\\n}\\r\\n.mt-12{\\n  margin-top: 3rem;\\n}\\r\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-2\\\\.5{\\n  margin-top: 0.625rem;\\n}\\r\\n.mt-24{\\n  margin-top: 6rem;\\n}\\r\\n.mt-4{\\n  margin-top: 1rem;\\n}\\r\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-auto{\\n  margin-top: auto;\\n}\\r\\n.block{\\n  display: block;\\n}\\r\\n.inline-block{\\n  display: inline-block;\\n}\\r\\n.flex{\\n  display: flex;\\n}\\r\\n.inline-flex{\\n  display: inline-flex;\\n}\\r\\n.table{\\n  display: table;\\n}\\r\\n.grid{\\n  display: grid;\\n}\\r\\n.hidden{\\n  display: none;\\n}\\r\\n.aspect-square{\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video{\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.size-4{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n.h-1{\\n  height: 0.25rem;\\n}\\r\\n.h-1\\\\.5{\\n  height: 0.375rem;\\n}\\r\\n.h-10{\\n  height: 2.5rem;\\n}\\r\\n.h-11{\\n  height: 2.75rem;\\n}\\r\\n.h-12{\\n  height: 3rem;\\n}\\r\\n.h-2{\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5{\\n  height: 0.625rem;\\n}\\r\\n.h-24{\\n  height: 6rem;\\n}\\r\\n.h-3{\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5{\\n  height: 0.875rem;\\n}\\r\\n.h-4{\\n  height: 1rem;\\n}\\r\\n.h-5{\\n  height: 1.25rem;\\n}\\r\\n.h-6{\\n  height: 1.5rem;\\n}\\r\\n.h-7{\\n  height: 1.75rem;\\n}\\r\\n.h-8{\\n  height: 2rem;\\n}\\r\\n.h-9{\\n  height: 2.25rem;\\n}\\r\\n.h-96{\\n  height: 24rem;\\n}\\r\\n.h-\\\\[1px\\\\]{\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-navigation-menu-viewport-height\\\\)\\\\]{\\n  height: var(--radix-navigation-menu-viewport-height);\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\]{\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto{\\n  height: auto;\\n}\\r\\n.h-full{\\n  height: 100%;\\n}\\r\\n.h-px{\\n  height: 1px;\\n}\\r\\n.h-svh{\\n  height: 100svh;\\n}\\r\\n.max-h-96{\\n  max-height: 24rem;\\n}\\r\\n.max-h-\\\\[300px\\\\]{\\n  max-height: 300px;\\n}\\r\\n.max-h-screen{\\n  max-height: 100vh;\\n}\\r\\n.min-h-0{\\n  min-height: 0px;\\n}\\r\\n.min-h-\\\\[80px\\\\]{\\n  min-height: 80px;\\n}\\r\\n.min-h-screen{\\n  min-height: 100vh;\\n}\\r\\n.min-h-svh{\\n  min-height: 100svh;\\n}\\r\\n.w-0{\\n  width: 0px;\\n}\\r\\n.w-1{\\n  width: 0.25rem;\\n}\\r\\n.w-10{\\n  width: 2.5rem;\\n}\\r\\n.w-11{\\n  width: 2.75rem;\\n}\\r\\n.w-12{\\n  width: 3rem;\\n}\\r\\n.w-2{\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5{\\n  width: 0.625rem;\\n}\\r\\n.w-24{\\n  width: 6rem;\\n}\\r\\n.w-3{\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5{\\n  width: 0.875rem;\\n}\\r\\n.w-3\\\\/4{\\n  width: 75%;\\n}\\r\\n.w-4{\\n  width: 1rem;\\n}\\r\\n.w-5{\\n  width: 1.25rem;\\n}\\r\\n.w-6{\\n  width: 1.5rem;\\n}\\r\\n.w-64{\\n  width: 16rem;\\n}\\r\\n.w-7{\\n  width: 1.75rem;\\n}\\r\\n.w-72{\\n  width: 18rem;\\n}\\r\\n.w-8{\\n  width: 2rem;\\n}\\r\\n.w-9{\\n  width: 2.25rem;\\n}\\r\\n.w-96{\\n  width: 24rem;\\n}\\r\\n.w-\\\\[--sidebar-width\\\\]{\\n  width: var(--sidebar-width);\\n}\\r\\n.w-\\\\[100px\\\\]{\\n  width: 100px;\\n}\\r\\n.w-\\\\[1px\\\\]{\\n  width: 1px;\\n}\\r\\n.w-auto{\\n  width: auto;\\n}\\r\\n.w-full{\\n  width: 100%;\\n}\\r\\n.w-max{\\n  width: -moz-max-content;\\n  width: max-content;\\n}\\r\\n.w-px{\\n  width: 1px;\\n}\\r\\n.min-w-0{\\n  min-width: 0px;\\n}\\r\\n.min-w-5{\\n  min-width: 1.25rem;\\n}\\r\\n.min-w-\\\\[12rem\\\\]{\\n  min-width: 12rem;\\n}\\r\\n.min-w-\\\\[8rem\\\\]{\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\]{\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl{\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl{\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl{\\n  max-width: 56rem;\\n}\\r\\n.max-w-6xl{\\n  max-width: 72rem;\\n}\\r\\n.max-w-7xl{\\n  max-width: 80rem;\\n}\\r\\n.max-w-\\\\[--skeleton-width\\\\]{\\n  max-width: var(--skeleton-width);\\n}\\r\\n.max-w-lg{\\n  max-width: 32rem;\\n}\\r\\n.max-w-max{\\n  max-width: -moz-max-content;\\n  max-width: max-content;\\n}\\r\\n.max-w-md{\\n  max-width: 28rem;\\n}\\r\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\r\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\r\\n.shrink-0{\\n  flex-shrink: 0;\\n}\\r\\n.grow{\\n  flex-grow: 1;\\n}\\r\\n.grow-0{\\n  flex-grow: 0;\\n}\\r\\n.basis-full{\\n  flex-basis: 100%;\\n}\\r\\n.caption-bottom{\\n  caption-side: bottom;\\n}\\r\\n.border-collapse{\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-px{\\n  --tw-translate-x: -1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-1\\\\/2{\\n  --tw-translate-x: 50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\]{\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-px{\\n  --tw-translate-x: 1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1\\\\/2{\\n  --tw-translate-y: 50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\]{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45{\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-90{\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-0{\\n  --tw-scale-x: 0;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.animate-\\\\[scale-x-100_1s_ease-out_0\\\\.5s_forwards\\\\]{\\n  animation: scale-x-100 1s ease-out 0.5s forwards;\\n}\\r\\n@keyframes pulse{\\r\\n\\r\\n  50%{\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin{\\r\\n\\r\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default{\\n  cursor: default;\\n}\\r\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\r\\n.touch-none{\\n  touch-action: none;\\n}\\r\\n.select-none{\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none{\\n  resize: none;\\n}\\r\\n.list-none{\\n  list-style-type: none;\\n}\\r\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2{\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-row{\\n  flex-direction: row;\\n}\\r\\n.flex-row-reverse{\\n  flex-direction: row-reverse;\\n}\\r\\n.flex-col{\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse{\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap{\\n  flex-wrap: wrap;\\n}\\r\\n.items-start{\\n  align-items: flex-start;\\n}\\r\\n.items-end{\\n  align-items: flex-end;\\n}\\r\\n.items-center{\\n  align-items: center;\\n}\\r\\n.items-stretch{\\n  align-items: stretch;\\n}\\r\\n.justify-center{\\n  justify-content: center;\\n}\\r\\n.justify-between{\\n  justify-content: space-between;\\n}\\r\\n.gap-1{\\n  gap: 0.25rem;\\n}\\r\\n.gap-1\\\\.5{\\n  gap: 0.375rem;\\n}\\r\\n.gap-12{\\n  gap: 3rem;\\n}\\r\\n.gap-2{\\n  gap: 0.5rem;\\n}\\r\\n.gap-3{\\n  gap: 0.75rem;\\n}\\r\\n.gap-4{\\n  gap: 1rem;\\n}\\r\\n.gap-6{\\n  gap: 1.5rem;\\n}\\r\\n.gap-8{\\n  gap: 2rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-x-reverse > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 1;\\n}\\r\\n.overflow-auto{\\n  overflow: auto;\\n}\\r\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\r\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\r\\n.overflow-x-hidden{\\n  overflow-x: hidden;\\n}\\r\\n.whitespace-nowrap{\\n  white-space: nowrap;\\n}\\r\\n.break-words{\\n  overflow-wrap: break-word;\\n}\\r\\n.rounded-\\\\[2px\\\\]{\\n  border-radius: 2px;\\n}\\r\\n.rounded-\\\\[inherit\\\\]{\\n  border-radius: inherit;\\n}\\r\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg{\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md{\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm{\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-t-\\\\[10px\\\\]{\\n  border-top-left-radius: 10px;\\n  border-top-right-radius: 10px;\\n}\\r\\n.rounded-tl-sm{\\n  border-top-left-radius: calc(var(--radius) - 4px);\\n}\\r\\n.border{\\n  border-width: 1px;\\n}\\r\\n.border-0{\\n  border-width: 0px;\\n}\\r\\n.border-2{\\n  border-width: 2px;\\n}\\r\\n.border-\\\\[1\\\\.5px\\\\]{\\n  border-width: 1.5px;\\n}\\r\\n.border-y{\\n  border-top-width: 1px;\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\r\\n.border-l{\\n  border-left-width: 1px;\\n}\\r\\n.border-r{\\n  border-right-width: 1px;\\n}\\r\\n.border-t{\\n  border-top-width: 1px;\\n}\\r\\n.border-dashed{\\n  border-style: dashed;\\n}\\r\\n.border-\\\\[--color-border\\\\]{\\n  border-color: var(--color-border);\\n}\\r\\n.border-border\\\\/50{\\n  border-color: hsl(var(--border) / 0.5);\\n}\\r\\n.border-destructive{\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50{\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-100{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-800{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-input{\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-primary{\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-sidebar-border{\\n  border-color: hsl(var(--sidebar-border));\\n}\\r\\n.border-transparent{\\n  border-color: transparent;\\n}\\r\\n.border-white{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-l-transparent{\\n  border-left-color: transparent;\\n}\\r\\n.border-t-transparent{\\n  border-top-color: transparent;\\n}\\r\\n.bg-\\\\[--color-bg\\\\]{\\n  background-color: var(--color-bg);\\n}\\r\\n.bg-accent{\\n  background-color: hsl(var(--accent));\\n}\\r\\n.bg-background{\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-black\\\\/80{\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-border{\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card{\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive{\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-foreground{\\n  background-color: hsl(var(--foreground));\\n}\\r\\n.bg-gray-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-900{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-muted{\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-muted\\\\/50{\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\r\\n.bg-popover{\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary{\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-secondary{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-sidebar{\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n.bg-sidebar-border{\\n  background-color: hsl(var(--sidebar-border));\\n}\\r\\n.bg-transparent{\\n  background-color: transparent;\\n}\\r\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/80{\\n  background-color: rgb(255 255 255 / 0.8);\\n}\\r\\n.bg-white\\\\/90{\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n.bg-white\\\\/95{\\n  background-color: rgb(255 255 255 / 0.95);\\n}\\r\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-blue-100{\\n  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-400\\\\/20{\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-50\\\\/50{\\n  --tw-gradient-from: rgb(239 246 255 / 0.5) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-600{\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-emerald-500{\\n  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50{\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900{\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500{\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-indigo-500{\\n  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-400\\\\/20{\\n  --tw-gradient-from: rgb(192 132 252 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-slate-50{\\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-yellow-500{\\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-blue-800{\\n  --tw-gradient-to: rgb(30 64 175 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #1e40af var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-transparent{\\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-white{\\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-blue-50{\\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-500{\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\r\\n.to-green-500{\\n  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-500{\\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-400\\\\/20{\\n  --tw-gradient-to: rgb(244 114 182 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-500{\\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-100{\\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-400\\\\/20{\\n  --tw-gradient-to: rgb(192 132 252 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-50\\\\/50{\\n  --tw-gradient-to: rgb(250 245 255 / 0.5) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500{\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-800{\\n  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);\\n}\\r\\n.to-teal-500{\\n  --tw-gradient-to: #14b8a6 var(--tw-gradient-to-position);\\n}\\r\\n.bg-clip-text{\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current{\\n  fill: currentColor;\\n}\\r\\n.p-0{\\n  padding: 0px;\\n}\\r\\n.p-1{\\n  padding: 0.25rem;\\n}\\r\\n.p-2{\\n  padding: 0.5rem;\\n}\\r\\n.p-3{\\n  padding: 0.75rem;\\n}\\r\\n.p-4{\\n  padding: 1rem;\\n}\\r\\n.p-6{\\n  padding: 1.5rem;\\n}\\r\\n.p-\\\\[1px\\\\]{\\n  padding: 1px;\\n}\\r\\n.px-1{\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5{\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5{\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6{\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8{\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-0\\\\.5{\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\r\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-12{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\r\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20{\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4{\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6{\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.pb-3{\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4{\\n  padding-bottom: 1rem;\\n}\\r\\n.pl-2\\\\.5{\\n  padding-left: 0.625rem;\\n}\\r\\n.pl-4{\\n  padding-left: 1rem;\\n}\\r\\n.pl-8{\\n  padding-left: 2rem;\\n}\\r\\n.pr-2{\\n  padding-right: 0.5rem;\\n}\\r\\n.pr-2\\\\.5{\\n  padding-right: 0.625rem;\\n}\\r\\n.pr-8{\\n  padding-right: 2rem;\\n}\\r\\n.pt-0{\\n  padding-top: 0px;\\n}\\r\\n.pt-1{\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-20{\\n  padding-top: 5rem;\\n}\\r\\n.pt-3{\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4{\\n  padding-top: 1rem;\\n}\\r\\n.pt-6{\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left{\\n  text-align: left;\\n}\\r\\n.text-center{\\n  text-align: center;\\n}\\r\\n.text-right{\\n  text-align: right;\\n}\\r\\n.align-middle{\\n  vertical-align: middle;\\n}\\r\\n.font-cairo{\\n  font-family: Cairo, sans-serif;\\n}\\r\\n.font-inter{\\n  font-family: Inter, sans-serif;\\n}\\r\\n.font-mono{\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\r\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-9xl{\\n  font-size: 8rem;\\n  line-height: 1;\\n}\\r\\n.text-\\\\[0\\\\.8rem\\\\]{\\n  font-size: 0.8rem;\\n}\\r\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold{\\n  font-weight: 700;\\n}\\r\\n.font-medium{\\n  font-weight: 500;\\n}\\r\\n.font-normal{\\n  font-weight: 400;\\n}\\r\\n.font-semibold{\\n  font-weight: 600;\\n}\\r\\n.tabular-nums{\\n  --tw-numeric-spacing: tabular-nums;\\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\\n}\\r\\n.leading-none{\\n  line-height: 1;\\n}\\r\\n.leading-relaxed{\\n  line-height: 1.625;\\n}\\r\\n.tracking-tight{\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-widest{\\n  letter-spacing: 0.1em;\\n}\\r\\n.text-accent-foreground{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n.text-blue-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground{\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current{\\n  color: currentColor;\\n}\\r\\n.text-destructive{\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground{\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground{\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-foreground\\\\/50{\\n  color: hsl(var(--foreground) / 0.5);\\n}\\r\\n.text-gray-200{\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground{\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary{\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-secondary-foreground{\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-sidebar-foreground{\\n  color: hsl(var(--sidebar-foreground));\\n}\\r\\n.text-sidebar-foreground\\\\/70{\\n  color: hsl(var(--sidebar-foreground) / 0.7);\\n}\\r\\n.text-transparent{\\n  color: transparent;\\n}\\r\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4{\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0{\\n  opacity: 0;\\n}\\r\\n.opacity-5{\\n  opacity: 0.05;\\n}\\r\\n.opacity-50{\\n  opacity: 0.5;\\n}\\r\\n.opacity-60{\\n  opacity: 0.6;\\n}\\r\\n.opacity-70{\\n  opacity: 0.7;\\n}\\r\\n.opacity-90{\\n  opacity: 0.9;\\n}\\r\\n.shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-border\\\\)\\\\)\\\\]{\\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));\\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-none{\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline-none{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline{\\n  outline-style: solid;\\n}\\r\\n.ring-0{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.ring-2{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.ring-gray-200{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));\\n}\\r\\n.ring-ring{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n.ring-sidebar-ring{\\n  --tw-ring-color: hsl(var(--sidebar-ring));\\n}\\r\\n.ring-offset-background{\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur-3xl{\\n  --tw-blur: blur(64px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[left\\\\2c right\\\\2c width\\\\]{\\n  transition-property: left,right,width;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[margin\\\\2c opa\\\\]{\\n  transition-property: margin,opa;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[width\\\\2c height\\\\2c padding\\\\]{\\n  transition-property: width,height,padding;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-\\\\[width\\\\]{\\n  transition-property: width;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-shadow{\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-1000{\\n  transition-duration: 1000ms;\\n}\\r\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300{\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500{\\n  transition-duration: 500ms;\\n}\\r\\n.ease-in-out{\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-linear{\\n  transition-timing-function: linear;\\n}\\r\\n@keyframes enter{\\r\\n\\r\\n  from{\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit{\\r\\n\\r\\n  to{\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-0{\\n  --tw-enter-opacity: 0;\\n}\\r\\n.fade-in-80{\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.zoom-in-95{\\n  --tw-enter-scale: .95;\\n}\\r\\n.duration-1000{\\n  animation-duration: 1000ms;\\n}\\r\\n.duration-200{\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300{\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500{\\n  animation-duration: 500ms;\\n}\\r\\n.ease-in-out{\\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-linear{\\n  animation-timing-function: linear;\\n}\\r\\n\\r\\n/* Simple animations */\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n/* Language transition effects */\\r\\n[dir=\\\"rtl\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n[dir=\\\"ltr\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n/* Text direction transitions */\\r\\n.text-transition {\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n/* Performance optimizations */\\r\\n* {\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n/* Reduce motion for accessibility */\\r\\n@media (prefers-reduced-motion: reduce) {\\r\\n  *,\\r\\n  *::before,\\r\\n  *::after {\\r\\n    animation-duration: 0.01ms !important;\\r\\n    animation-iteration-count: 1 !important;\\r\\n    transition-duration: 0.01ms !important;\\r\\n    scroll-behavior: auto !important;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n/* Text selection */\\r\\n::-moz-selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n::selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n/* Focus styles */\\r\\n.focus-visible:focus {\\r\\n  outline: 2px solid #3b82f6;\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n/* Line clamp utility */\\r\\n.line-clamp-3 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Custom avatar glow effect */\\r\\n.avatar-glow {\\r\\n  box-shadow:\\r\\n    0 0 20px rgba(59, 130, 246, 0.3),\\r\\n    0 0 40px rgba(139, 92, 246, 0.2),\\r\\n    0 0 60px rgba(236, 72, 153, 0.1);\\r\\n}\\r\\n\\r\\n.avatar-glow:hover {\\r\\n  box-shadow:\\r\\n    0 0 30px rgba(59, 130, 246, 0.4),\\r\\n    0 0 60px rgba(139, 92, 246, 0.3),\\r\\n    0 0 90px rgba(236, 72, 153, 0.2);\\r\\n}\\r\\n\\r\\n/* Scroll Animation Classes - Enhanced for smoothness */\\r\\n.scroll-animate {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-100 {\\r\\n  transition-delay: 150ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-200 {\\r\\n  transition-delay: 300ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-300 {\\r\\n  transition-delay: 450ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-400 {\\r\\n  transition-delay: 600ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-500 {\\r\\n  transition-delay: 750ms;\\r\\n}\\r\\n\\r\\n/* Stagger animation for children - Enhanced */\\r\\n.stagger-children > * {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.stagger-children > *:nth-child(1) { transition-delay: 0ms; }\\r\\n.stagger-children > *:nth-child(2) { transition-delay: 150ms; }\\r\\n.stagger-children > *:nth-child(3) { transition-delay: 300ms; }\\r\\n.stagger-children > *:nth-child(4) { transition-delay: 450ms; }\\r\\n.stagger-children > *:nth-child(5) { transition-delay: 600ms; }\\r\\n.stagger-children > *:nth-child(6) { transition-delay: 750ms; }\\r\\n.stagger-children > *:nth-child(7) { transition-delay: 900ms; }\\r\\n.stagger-children > *:nth-child(8) { transition-delay: 1050ms; }\\r\\n\\r\\n/* Parallax effect */\\r\\n.parallax-slow {\\r\\n  transform: translateY(var(--scroll-y, 0) * 0.5);\\r\\n}\\r\\n\\r\\n.parallax-fast {\\r\\n  transform: translateY(var(--scroll-y, 0) * -0.3);\\r\\n}\\r\\n\\r\\n/* Reveal animations */\\r\\n.reveal-up {\\r\\n  opacity: 0;\\r\\n  transform: translateY(50px);\\r\\n}\\r\\n\\r\\n.reveal-up.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.reveal-left {\\r\\n  opacity: 0;\\r\\n  transform: translateX(-50px);\\r\\n}\\r\\n\\r\\n.reveal-left.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-right {\\r\\n  opacity: 0;\\r\\n  transform: translateX(50px);\\r\\n}\\r\\n\\r\\n.reveal-right.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-scale {\\r\\n  opacity: 0;\\r\\n  transform: scale(0.8);\\r\\n}\\r\\n\\r\\n.reveal-scale.revealed {\\r\\n  opacity: 1;\\r\\n  transform: scale(1);\\r\\n}\\r\\n\\r\\n/* Hover effects for cards - Enhanced smoothness */\\r\\n.card-hover {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow;\\r\\n}\\r\\n\\r\\n.card-hover:hover {\\r\\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\\r\\n  box-shadow:\\r\\n    0 32px 64px -12px rgba(0, 0, 0, 0.25),\\r\\n    0 0 0 1px rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n/* Enhanced gradient text animation */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981, #f59e0b);\\r\\n  background-size: 400% 400%;\\r\\n  animation: gradient-shift 8s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  will-change: background-position;\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Typing animation */\\r\\n.typing-animation {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid #3b82f6;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink-caret {\\r\\n  from, to {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  50% {\\r\\n    border-color: #3b82f6;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced magnetic effect */\\r\\n.magnetic {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform;\\r\\n}\\r\\n\\r\\n.magnetic:hover {\\r\\n  transform: scale(1.08) translateY(-2px);\\r\\n  filter: brightness(1.1);\\r\\n}\\r\\n\\r\\n/* Smooth button transitions */\\r\\n.btn-smooth {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow, background-color;\\r\\n}\\r\\n\\r\\n.btn-smooth:hover {\\r\\n  transform: translateY(-2px) scale(1.02);\\r\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.btn-smooth:active {\\r\\n  transform: translateY(0) scale(0.98);\\r\\n  transition-duration: 0.1s;\\r\\n}\\r\\n\\r\\n/* Glitch effect */\\r\\n.glitch {\\r\\n  position: relative;\\r\\n  animation: glitch 2s infinite;\\r\\n}\\r\\n\\r\\n.glitch::before,\\r\\n.glitch::after {\\r\\n  content: attr(data-text);\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.glitch::before {\\r\\n  animation: glitch-1 0.5s infinite;\\r\\n  color: #ff0000;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.glitch::after {\\r\\n  animation: glitch-2 0.5s infinite;\\r\\n  color: #00ff00;\\r\\n  z-index: -2;\\r\\n}\\r\\n\\r\\n@keyframes glitch {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-2px, 2px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-2px, -2px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(2px, 2px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(2px, -2px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-1 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-2 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* RTL Support */\\r\\n[dir=\\\"rtl\\\"] .flex-row {\\r\\n  flex-direction: row-reverse;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .flex-row-reverse {\\r\\n  flex-direction: row;\\r\\n}\\r\\n\\r\\n/* RTL Space between */\\r\\n[dir=\\\"rtl\\\"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n/* RTL Gap utilities */\\r\\n[dir=\\\"rtl\\\"] .gap-2 {\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-3 {\\r\\n  gap: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-4 {\\r\\n  gap: 1rem;\\r\\n}\\r\\n\\r\\n/* RTL Icon spacing */\\r\\n[dir=\\\"rtl\\\"] .mr-2 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-2 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .mr-3 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-3 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.75rem;\\r\\n}\\r\\n\\r\\n/* RTL Text alignment */\\r\\n[dir=\\\"rtl\\\"] .text-left {\\r\\n  text-align: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .text-right {\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n/* RTL Float */\\r\\n[dir=\\\"rtl\\\"] .float-left {\\r\\n  float: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .float-right {\\r\\n  float: left;\\r\\n}\\r\\n\\r\\n/* RTL Positioning */\\r\\n[dir=\\\"rtl\\\"] .left-0 {\\r\\n  left: auto;\\r\\n  right: 0;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .right-0 {\\r\\n  right: auto;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\n/* RTL Border radius */\\r\\n[dir=\\\"rtl\\\"] .rounded-l {\\r\\n  border-top-left-radius: 0;\\r\\n  border-bottom-left-radius: 0;\\r\\n  border-top-right-radius: 0.25rem;\\r\\n  border-bottom-right-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .rounded-r {\\r\\n  border-top-right-radius: 0;\\r\\n  border-bottom-right-radius: 0;\\r\\n  border-top-left-radius: 0.25rem;\\r\\n  border-bottom-left-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */\\r\\n\\r\\n/* Enhanced animations and effects */\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-20px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-25px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.8;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: left center;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: right center;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scale-x-100 {\\r\\n  from {\\r\\n    transform: scaleX(0);\\r\\n  }\\r\\n  to {\\r\\n    transform: scaleX(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 4s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Smooth scroll behavior */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image: \\r\\n    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced gradients */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Glassmorphism effect */\\r\\n.glass-effect {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  -webkit-backdrop-filter: blur(10px);\\r\\n          backdrop-filter: blur(10px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #5a6fd8, #6a419a);\\r\\n}\\r\\n\\r\\n.file\\\\:border-0::file-selector-button{\\n  border-width: 0px;\\n}\\r\\n\\r\\n.file\\\\:bg-transparent::file-selector-button{\\n  background-color: transparent;\\n}\\r\\n\\r\\n.file\\\\:text-sm::file-selector-button{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n\\r\\n.file\\\\:font-medium::file-selector-button{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.file\\\\:text-foreground::file-selector-button{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.after\\\\:absolute::after{\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n\\r\\n.after\\\\:-inset-2::after{\\n  content: var(--tw-content);\\n  inset: -0.5rem;\\n}\\r\\n\\r\\n.after\\\\:inset-y-0::after{\\n  content: var(--tw-content);\\n  top: 0px;\\n  bottom: 0px;\\n}\\r\\n\\r\\n.after\\\\:left-1\\\\/2::after{\\n  content: var(--tw-content);\\n  left: 50%;\\n}\\r\\n\\r\\n.after\\\\:w-1::after{\\n  content: var(--tw-content);\\n  width: 0.25rem;\\n}\\r\\n\\r\\n.after\\\\:w-\\\\[2px\\\\]::after{\\n  content: var(--tw-content);\\n  width: 2px;\\n}\\r\\n\\r\\n.after\\\\:-translate-x-1\\\\/2::after{\\n  content: var(--tw-content);\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.first\\\\:rounded-l-md:first-child{\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.first\\\\:border-l:first-child{\\n  border-left-width: 1px;\\n}\\r\\n\\r\\n.last\\\\:rounded-r-md:last-child{\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.focus-within\\\\:relative:focus-within{\\n  position: relative;\\n}\\r\\n\\r\\n.focus-within\\\\:z-20:focus-within{\\n  z-index: 20;\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:scale-\\\\[1\\\\.02\\\\]:hover{\\n  --tw-scale-x: 1.02;\\n  --tw-scale-y: 1.02;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/80:hover{\\n  background-color: hsl(var(--destructive) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover{\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-200:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-800:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-muted:hover{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.hover\\\\:bg-muted\\\\/50:hover{\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary:hover{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/80:hover{\\n  background-color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover{\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary:hover{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover{\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-sidebar-accent:hover{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover{\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:from-blue-700:hover{\\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n\\r\\n.hover\\\\:to-purple-700:hover{\\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-blue-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-gray-900:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-muted-foreground:hover{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary-foreground:hover{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-sidebar-accent-foreground:hover{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover{\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover{\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:shadow-2xl:hover{\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-accent\\\\)\\\\)\\\\]:hover{\\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));\\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-md:hover{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:shadow-xl:hover{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.hover\\\\:after\\\\:bg-sidebar-border:hover::after{\\n  content: var(--tw-content);\\n  background-color: hsl(var(--sidebar-border));\\n}\\r\\n\\r\\n.focus\\\\:border-blue-500:focus{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:bg-primary:focus{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:text-primary-foreground:focus{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.focus\\\\:opacity-100:focus{\\n  opacity: 1;\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-blue-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus{\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-1:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible{\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-sidebar-ring:focus-visible{\\n  --tw-ring-color: hsl(var(--sidebar-ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-1:focus-visible{\\n  --tw-ring-offset-width: 1px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible{\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-background:focus-visible{\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n\\r\\n.active\\\\:bg-sidebar-accent:active{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.active\\\\:text-sidebar-accent-foreground:active{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group\\\\/menu-item:focus-within .group-focus-within\\\\/menu-item\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group\\\\/item:hover .group-hover\\\\/item\\\\:scale-125{\\n  --tw-scale-x: 1.25;\\n  --tw-scale-y: 1.25;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:scale-110{\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group\\\\/item:hover .group-hover\\\\/item\\\\:text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-blue-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:text-blue-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group\\\\/menu-item:hover .group-hover\\\\/menu-item\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100{\\n  opacity: 1;\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:border-muted\\\\/40{\\n  border-color: hsl(var(--muted) / 0.4);\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:border-border{\\n  border-color: hsl(var(--border));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:bg-muted{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:bg-primary{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:bg-background{\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:text-red-300{\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:text-muted-foreground{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.group.toast .group-\\\\[\\\\.toast\\\\]\\\\:text-primary-foreground{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:text-foreground{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.group.toaster .group-\\\\[\\\\.toaster\\\\]\\\\:shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:border-destructive\\\\/30:hover{\\n  border-color: hsl(var(--destructive) / 0.3);\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:bg-destructive:hover{\\n  background-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-destructive-foreground:hover{\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-red-50:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-destructive:focus{\\n  --tw-ring-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-red-400:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\\n}\\r\\n\\r\\n.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-offset-red-600:focus{\\n  --tw-ring-offset-color: #dc2626;\\n}\\r\\n\\r\\n.peer\\\\/menu-button:hover ~ .peer-hover\\\\/menu-button\\\\:text-sidebar-accent-foreground{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed{\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70{\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.has-\\\\[\\\\[data-variant\\\\=inset\\\\]\\\\]\\\\:bg-sidebar:has([data-variant=inset]){\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n\\r\\n.has-\\\\[\\\\:disabled\\\\]\\\\:opacity-50:has(:disabled){\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group\\\\/menu-item:has([data-sidebar=menu-action]) .group-has-\\\\[\\\\[data-sidebar\\\\=menu-action\\\\]\\\\]\\\\/menu-item\\\\:pr-8{\\n  padding-right: 2rem;\\n}\\r\\n\\r\\n.aria-disabled\\\\:pointer-events-none[aria-disabled=\\\"true\\\"]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.aria-disabled\\\\:opacity-50[aria-disabled=\\\"true\\\"]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.aria-selected\\\\:bg-accent[aria-selected=\\\"true\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.aria-selected\\\\:bg-accent\\\\/50[aria-selected=\\\"true\\\"]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.aria-selected\\\\:text-accent-foreground[aria-selected=\\\"true\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.aria-selected\\\\:text-muted-foreground[aria-selected=\\\"true\\\"]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.aria-selected\\\\:opacity-100[aria-selected=\\\"true\\\"]{\\n  opacity: 1;\\n}\\r\\n\\r\\n.aria-selected\\\\:opacity-30[aria-selected=\\\"true\\\"]{\\n  opacity: 0.3;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none[data-disabled=\\\"true\\\"]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled]{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:h-px[data-panel-group-direction=\\\"vertical\\\"]{\\n  height: 1px;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:w-full[data-panel-group-direction=\\\"vertical\\\"]{\\n  width: 100%;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"]{\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"]{\\n  --tw-translate-x: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"]{\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"]{\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:translate-x-5[data-state=\\\"checked\\\"]{\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:translate-x-0[data-state=\\\"unchecked\\\"]{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=cancel\\\\]\\\\:translate-x-0[data-swipe=\\\"cancel\\\"]{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=end\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-end-x\\\\)\\\\][data-swipe=\\\"end\\\"]{\\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=move\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-move-x\\\\)\\\\][data-swipe=\\\"move\\\"]{\\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n@keyframes accordion-up{\\r\\n\\r\\n  from{\\n    height: var(--radix-accordion-content-height);\\n  }\\r\\n\\r\\n  to{\\n    height: 0;\\n  }\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up[data-state=\\\"closed\\\"]{\\n  animation: accordion-up 0.2s ease-out;\\n}\\r\\n\\r\\n@keyframes accordion-down{\\r\\n\\r\\n  from{\\n    height: 0;\\n  }\\r\\n\\r\\n  to{\\n    height: var(--radix-accordion-content-height);\\n  }\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down[data-state=\\\"open\\\"]{\\n  animation: accordion-down 0.2s ease-out;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:flex-col[data-panel-group-direction=\\\"vertical\\\"]{\\n  flex-direction: column;\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:bg-sidebar-accent[data-active=\\\"true\\\"]{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.data-\\\\[active\\\\]\\\\:bg-accent\\\\/50[data-active]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.data-\\\\[selected\\\\=\\\\'true\\\\'\\\\]\\\\:bg-accent[data-selected='true']{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"]{\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary[data-state=\\\"checked\\\"]{\\n  background-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=on\\\\]\\\\:bg-accent[data-state=\\\"on\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent\\\\/50[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--secondary));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted[data-state=\\\"selected\\\"]{\\n  background-color: hsl(var(--muted));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:bg-input[data-state=\\\"unchecked\\\"]{\\n  background-color: hsl(var(--input));\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:font-medium[data-active=\\\"true\\\"]{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.data-\\\\[active\\\\=true\\\\]\\\\:text-sidebar-accent-foreground[data-active=\\\"true\\\"]{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[selected\\\\=true\\\\]\\\\:text-accent-foreground[data-selected=\\\"true\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"]{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground[data-state=\\\"checked\\\"]{\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=on\\\\]\\\\:text-accent-foreground[data-state=\\\"on\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground[data-state=\\\"open\\\"]{\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50[data-disabled=\\\"true\\\"]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled]{\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:opacity-100[data-state=\\\"open\\\"]{\\n  opacity: 1;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"]{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=move\\\\]\\\\:transition-none[data-swipe=\\\"move\\\"]{\\n  transition-property: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:duration-300[data-state=\\\"closed\\\"]{\\n  transition-duration: 300ms;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:duration-500[data-state=\\\"open\\\"]{\\n  transition-duration: 500ms;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:animate-in[data-motion^=\\\"from-\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=visible\\\\]\\\\:animate-in[data-state=\\\"visible\\\"]{\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:animate-out[data-motion^=\\\"to-\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=hidden\\\\]\\\\:animate-out[data-state=\\\"hidden\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[swipe\\\\=end\\\\]\\\\:animate-out[data-swipe=\\\"end\\\"]{\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:fade-in[data-motion^=\\\"from-\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:fade-out[data-motion^=\\\"to-\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-80[data-state=\\\"closed\\\"]{\\n  --tw-exit-opacity: 0.8;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=hidden\\\\]\\\\:fade-out[data-state=\\\"hidden\\\"]{\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=visible\\\\]\\\\:fade-in[data-state=\\\"visible\\\"]{\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"]{\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-90[data-state=\\\"open\\\"]{\\n  --tw-enter-scale: .9;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"]{\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=from-end\\\\]\\\\:slide-in-from-right-52[data-motion=\\\"from-end\\\"]{\\n  --tw-enter-translate-x: 13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=from-start\\\\]\\\\:slide-in-from-left-52[data-motion=\\\"from-start\\\"]{\\n  --tw-enter-translate-x: -13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=to-end\\\\]\\\\:slide-out-to-right-52[data-motion=\\\"to-end\\\"]{\\n  --tw-exit-translate-x: 13rem;\\n}\\r\\n\\r\\n.data-\\\\[motion\\\\=to-start\\\\]\\\\:slide-out-to-left-52[data-motion=\\\"to-start\\\"]{\\n  --tw-exit-translate-x: -13rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2[data-side=\\\"bottom\\\"]{\\n  --tw-enter-translate-y: -0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2[data-side=\\\"left\\\"]{\\n  --tw-enter-translate-x: 0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2[data-side=\\\"right\\\"]{\\n  --tw-enter-translate-x: -0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2[data-side=\\\"top\\\"]{\\n  --tw-enter-translate-y: 0.5rem;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right-full[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top[data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"]{\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-x: 100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-full[data-state=\\\"open\\\"]{\\n  --tw-enter-translate-y: -100%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:duration-300[data-state=\\\"closed\\\"]{\\n  animation-duration: 300ms;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:duration-500[data-state=\\\"open\\\"]{\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:left-0[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  left: 0px;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:h-1[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  height: 0.25rem;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:w-full[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  width: 100%;\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:-translate-y-1\\\\/2[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[panel-group-direction\\\\=vertical\\\\]\\\\:after\\\\:translate-x-0[data-panel-group-direction=\\\"vertical\\\"]::after{\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:bg-sidebar-accent:hover[data-state=\\\"open\\\"]{\\n  background-color: hsl(var(--sidebar-accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:text-sidebar-accent-foreground:hover[data-state=\\\"open\\\"]{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:left-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\]{\\n  left: calc(var(--sidebar-width) * -1);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:right-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\]{\\n  right: calc(var(--sidebar-width) * -1);\\n}\\r\\n\\r\\n.group[data-side=\\\"left\\\"] .group-data-\\\\[side\\\\=left\\\\]\\\\:-right-4{\\n  right: -1rem;\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:left-0{\\n  left: 0px;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:-mt-8{\\n  margin-top: -2rem;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:hidden{\\n  display: none;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!size-8{\\n  width: 2rem !important;\\n  height: 2rem !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[--sidebar-width-icon\\\\]{\\n  width: var(--sidebar-width-icon);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)_\\\\+_theme\\\\(spacing\\\\.4\\\\)\\\\)\\\\]{\\n  width: calc(var(--sidebar-width-icon) + 1rem);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)_\\\\+_theme\\\\(spacing\\\\.4\\\\)_\\\\+2px\\\\)\\\\]{\\n  width: calc(var(--sidebar-width-icon) + 1rem + 2px);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:w-0{\\n  width: 0px;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:translate-x-0{\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-state=\\\"open\\\"] .group-data-\\\\[state\\\\=open\\\\]\\\\:rotate-180{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:overflow-hidden{\\n  overflow: hidden;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:rounded-lg{\\n  border-radius: var(--radius);\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border{\\n  border-width: 1px;\\n}\\r\\n\\r\\n.group[data-side=\\\"left\\\"] .group-data-\\\\[side\\\\=left\\\\]\\\\:border-r{\\n  border-right-width: 1px;\\n}\\r\\n\\r\\n.group[data-side=\\\"right\\\"] .group-data-\\\\[side\\\\=right\\\\]\\\\:border-l{\\n  border-left-width: 1px;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border-sidebar-border{\\n  border-color: hsl(var(--sidebar-border));\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!p-0{\\n  padding: 0px !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:\\\\!p-2{\\n  padding: 0.5rem !important;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"icon\\\"] .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:opacity-0{\\n  opacity: 0;\\n}\\r\\n\\r\\n.group[data-variant=\\\"floating\\\"] .group-data-\\\\[variant\\\\=floating\\\\]\\\\:shadow{\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:after\\\\:left-full::after{\\n  content: var(--tw-content);\\n  left: 100%;\\n}\\r\\n\\r\\n.group[data-collapsible=\\\"offcanvas\\\"] .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:hover\\\\:bg-sidebar:hover{\\n  background-color: hsl(var(--sidebar-background));\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"default\\\"] ~ .peer-data-\\\\[size\\\\=default\\\\]\\\\/menu-button\\\\:top-1\\\\.5{\\n  top: 0.375rem;\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"lg\\\"] ~ .peer-data-\\\\[size\\\\=lg\\\\]\\\\/menu-button\\\\:top-2\\\\.5{\\n  top: 0.625rem;\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-size=\\\"sm\\\"] ~ .peer-data-\\\\[size\\\\=sm\\\\]\\\\/menu-button\\\\:top-1{\\n  top: 0.25rem;\\n}\\r\\n\\r\\n.peer[data-variant=\\\"inset\\\"] ~ .peer-data-\\\\[variant\\\\=inset\\\\]\\\\:min-h-\\\\[calc\\\\(100svh-theme\\\\(spacing\\\\.4\\\\)\\\\)\\\\]{\\n  min-height: calc(100svh - 1rem);\\n}\\r\\n\\r\\n.peer\\\\/menu-button[data-active=\\\"true\\\"] ~ .peer-data-\\\\[active\\\\=true\\\\]\\\\/menu-button\\\\:text-sidebar-accent-foreground{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *){\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n@media (min-width: 640px){\\r\\n\\r\\n  .sm\\\\:bottom-0{\\n    bottom: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:right-0{\\n    right: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:top-auto{\\n    top: auto;\\n  }\\r\\n\\r\\n  .sm\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\r\\n\\r\\n  .sm\\\\:flex{\\n    display: flex;\\n  }\\r\\n\\r\\n  .sm\\\\:max-w-sm{\\n    max-width: 24rem;\\n  }\\r\\n\\r\\n  .sm\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .sm\\\\:flex-row{\\n    flex-direction: row;\\n  }\\r\\n\\r\\n  .sm\\\\:flex-row-reverse{\\n    flex-direction: row-reverse;\\n  }\\r\\n\\r\\n  .sm\\\\:flex-col{\\n    flex-direction: column;\\n  }\\r\\n\\r\\n  .sm\\\\:justify-end{\\n    justify-content: flex-end;\\n  }\\r\\n\\r\\n  .sm\\\\:gap-2\\\\.5{\\n    gap: 0.625rem;\\n  }\\r\\n\\r\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\r\\n\\r\\n  .sm\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\r\\n\\r\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\r\\n\\r\\n  .sm\\\\:rounded-lg{\\n    border-radius: var(--radius);\\n  }\\r\\n\\r\\n  .sm\\\\:px-3{\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\r\\n\\r\\n  .sm\\\\:px-6{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\r\\n\\r\\n  .sm\\\\:text-left{\\n    text-align: left;\\n  }\\r\\n\\r\\n  .data-\\\\[state\\\\=open\\\\]\\\\:sm\\\\:slide-in-from-bottom-full[data-state=\\\"open\\\"]{\\n    --tw-enter-translate-y: 100%;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px){\\r\\n\\r\\n  .md\\\\:absolute{\\n    position: absolute;\\n  }\\r\\n\\r\\n  .md\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\r\\n\\r\\n  .md\\\\:block{\\n    display: block;\\n  }\\r\\n\\r\\n  .md\\\\:flex{\\n    display: flex;\\n  }\\r\\n\\r\\n  .md\\\\:hidden{\\n    display: none;\\n  }\\r\\n\\r\\n  .md\\\\:w-\\\\[var\\\\(--radix-navigation-menu-viewport-width\\\\)\\\\]{\\n    width: var(--radix-navigation-menu-viewport-width);\\n  }\\r\\n\\r\\n  .md\\\\:w-auto{\\n    width: auto;\\n  }\\r\\n\\r\\n  .md\\\\:max-w-\\\\[420px\\\\]{\\n    max-width: 420px;\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .md\\\\:flex-row{\\n    flex-direction: row;\\n  }\\r\\n\\r\\n  .md\\\\:items-center{\\n    align-items: center;\\n  }\\r\\n\\r\\n  .md\\\\:justify-between{\\n    justify-content: space-between;\\n  }\\r\\n\\r\\n  .md\\\\:text-4xl{\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\r\\n\\r\\n  .md\\\\:text-5xl{\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\r\\n\\r\\n  .md\\\\:text-6xl{\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\r\\n\\r\\n  .md\\\\:text-sm{\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\r\\n\\r\\n  .md\\\\:text-xl{\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\r\\n\\r\\n  .md\\\\:opacity-0{\\n    opacity: 0;\\n  }\\r\\n\\r\\n  .after\\\\:md\\\\:hidden::after{\\n    content: var(--tw-content);\\n    display: none;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:m-2{\\n    margin: 0.5rem;\\n  }\\r\\n\\r\\n  .peer[data-state=\\\"collapsed\\\"][data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[state\\\\=collapsed\\\\]\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-2{\\n    margin-left: 0.5rem;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-0{\\n    margin-left: 0px;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:rounded-xl{\\n    border-radius: 0.75rem;\\n  }\\r\\n\\r\\n  .peer[data-variant=\\\"inset\\\"] ~ .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:shadow{\\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px){\\r\\n\\r\\n  .lg\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\r\\n\\r\\n  .lg\\\\:px-8{\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\r\\n\\r\\n  .lg\\\\:text-2xl{\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:bg-accent:has([aria-selected]){\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.first\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-l-md:has([aria-selected]):first-child{\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.last\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected]):last-child{\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-outside\\\\)\\\\]\\\\:bg-accent\\\\/50:has([aria-selected].day-outside){\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected].day-range-end){\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0:has([role=checkbox]){\\n  padding-right: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>button\\\\]\\\\:hidden>button{\\n  display: none;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>span\\\\:last-child\\\\]\\\\:truncate>span:last-child{\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span{\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div{\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg{\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg{\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg{\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5>svg{\\n  width: 0.875rem;\\n  height: 0.875rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-4>svg{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:h-2\\\\.5>svg{\\n  height: 0.625rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:h-3>svg{\\n  height: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:w-2\\\\.5>svg{\\n  width: 0.625rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:w-3>svg{\\n  width: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:shrink-0>svg{\\n  flex-shrink: 0;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg{\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg{\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-muted-foreground>svg{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-sidebar-accent-foreground>svg{\\n  color: hsl(var(--sidebar-accent-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~*{\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0:last-child>tr{\\n  border-bottom-width: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\[data-panel-group-direction\\\\=vertical\\\\]\\\\>div\\\\]\\\\:rotate-90[data-panel-group-direction=vertical]>div{\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180[data-state=open]>svg{\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-cartesian-axis-tick_text\\\\]\\\\:fill-muted-foreground .recharts-cartesian-axis-tick text{\\n  fill: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-cartesian-grid_line\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border\\\\/50 .recharts-cartesian-grid line[stroke='#ccc']{\\n  stroke: hsl(var(--border) / 0.5);\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-curve\\\\.recharts-tooltip-cursor\\\\]\\\\:stroke-border .recharts-curve.recharts-tooltip-cursor{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-dot\\\\[stroke\\\\=\\\\'\\\\#fff\\\\'\\\\]\\\\]\\\\:stroke-transparent .recharts-dot[stroke='#fff']{\\n  stroke: transparent;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-layer\\\\]\\\\:outline-none .recharts-layer{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-polar-grid_\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border .recharts-polar-grid [stroke='#ccc']{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-radial-bar-background-sector\\\\]\\\\:fill-muted .recharts-radial-bar-background-sector{\\n  fill: hsl(var(--muted));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-rectangle\\\\.recharts-tooltip-cursor\\\\]\\\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{\\n  fill: hsl(var(--muted));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-reference-line_\\\\[stroke\\\\=\\\\'\\\\#ccc\\\\'\\\\]\\\\]\\\\:stroke-border .recharts-reference-line [stroke='#ccc']{\\n  stroke: hsl(var(--border));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-sector\\\\[stroke\\\\=\\\\'\\\\#fff\\\\'\\\\]\\\\]\\\\:stroke-transparent .recharts-sector[stroke='#fff']{\\n  stroke: transparent;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-sector\\\\]\\\\:outline-none .recharts-sector{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\.recharts-surface\\\\]\\\\:outline-none .recharts-surface{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:px-2 [cmdk-group-heading]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:py-1\\\\.5 [cmdk-group-heading]{\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-xs [cmdk-group-heading]{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:font-medium [cmdk-group-heading]{\\n  font-weight: 500;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-muted-foreground [cmdk-group-heading]{\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\:not\\\\(\\\\[hidden\\\\]\\\\)_\\\\~\\\\[cmdk-group\\\\]\\\\]\\\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{\\n  padding-top: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\]\\\\:px-2 [cmdk-group]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:h-5 [cmdk-input-wrapper] svg{\\n  height: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:w-5 [cmdk-input-wrapper] svg{\\n  width: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-input\\\\]\\\\]\\\\:h-12 [cmdk-input]{\\n  height: 3rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:px-2 [cmdk-item]{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:py-3 [cmdk-item]{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:h-5 [cmdk-item] svg{\\n  height: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:w-5 [cmdk-item] svg{\\n  width: 1.25rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p{\\n  line-height: 1.625;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:pointer-events-none svg{\\n  pointer-events: none;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:size-4 svg{\\n  width: 1rem;\\n  height: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_svg\\\\]\\\\:shrink-0 svg{\\n  flex-shrink: 0;\\n}\\r\\n\\r\\n.\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 tr:last-child{\\n  border-width: 0px;\\n}\\r\\n\\r\\n.\\\\[\\\\&_tr\\\\]\\\\:border-b tr{\\n  border-bottom-width: 1px;\\n}\\r\\n\\r\\n[data-side=left][data-collapsible=offcanvas] .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-right-2{\\n  right: -0.5rem;\\n}\\r\\n\\r\\n[data-side=left][data-state=collapsed] .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-e-resize{\\n  cursor: e-resize;\\n}\\r\\n\\r\\n[data-side=left] .\\\\[\\\\[data-side\\\\=left\\\\]_\\\\&\\\\]\\\\:cursor-w-resize{\\n  cursor: w-resize;\\n}\\r\\n\\r\\n[data-side=right][data-collapsible=offcanvas] .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-left-2{\\n  left: -0.5rem;\\n}\\r\\n\\r\\n[data-side=right][data-state=collapsed] .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-w-resize{\\n  cursor: w-resize;\\n}\\r\\n\\r\\n[data-side=right] .\\\\[\\\\[data-side\\\\=right\\\\]_\\\\&\\\\]\\\\:cursor-e-resize{\\n  cursor: e-resize;\\n}\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/index.css\"],\"names\":[],\"mappings\":\";AACA,uGAAuG;AACvG,uGAAuG;;AAEvG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;;CAAc;;AAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;IAAA,uBAAc;IAAd,4BAAc;;IAAd,iBAAc;IAAd,iCAAc;;IAAd,oBAAc;IAAd,oCAAc;;IAAd,4BAAc;IAAd,iCAAc;;IAAd,0BAAc;IAAd,yCAAc;;IAAd,sBAAc;IAAd,qCAAc;;IAAd,uBAAc;IAAd,sCAAc;;IAAd,4BAAc;IAAd,qCAAc;;IAAd,2BAAc;IAAd,0BAAc;IAAd,sBAAc;;IAAd,gBAAc;;IAAd,8BAAc;IAAd,oCAAc;IAAd,+BAAc;IAAd,sCAAc;IAAd,gCAAc;IAAd,yCAAc;IAAd,6BAAc;IAAd,iCAAc;EAAA;;AAAd;IAAA,4BAAc;IAAd,yBAAc;;IAAd,sBAAc;IAAd,8BAAc;;IAAd,yBAAc;IAAd,iCAAc;;IAAd,sBAAc;IAAd,uCAAc;;IAAd,8BAAc;IAAd,mCAAc;;IAAd,0BAAc;IAAd,mCAAc;;IAAd,2BAAc;IAAd,gCAAc;;IAAd,4BAAc;IAAd,qCAAc;;IAAd,2BAAc;IAAd,0BAAc;IAAd,yBAAc;IAAd,kCAAc;IAAd,oCAAc;IAAd,kCAAc;IAAd,uCAAc;IAAd,gCAAc;IAAd,2CAAc;IAAd,gCAAc;IAAd,iCAAc;EAAA;;AAAd;EAAA;AAAc;;AAAd;EAAA,wCAAc;EAAd;AAAc;AAEd;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,SAAmB;EAAnB;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,WAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB,qDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,qCAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,yCAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,sBAAsB;;;;;;AAMtB,gCAAgC;AAChC;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA,+BAA+B;AAC/B;EACE,yBAAyB;AAC3B;;;;AAIA,8BAA8B;AAC9B;EACE,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,oCAAoC;AACpC;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;;;AAIA,mBAAmB;AACnB;EACE,mCAAmC;EACnC,cAAc;AAChB;AAHA;EACE,mCAAmC;EACnC,cAAc;AAChB;;AAEA,iBAAiB;AACjB;EACE,0BAA0B;EAC1B,mBAAmB;AACrB;;AAEA,uBAAuB;AACvB;EACE,oBAAoB;EACpB,qBAAqB;EACrB,aAAa;EACb,4BAA4B;EAC5B,gBAAgB;AAClB;;AAEA,8BAA8B;AAC9B;EACE;;;oCAGkC;AACpC;;AAEA;EACE;;;oCAGkC;AACpC;;AAEA,uDAAuD;AACvD;EACE,yDAAyD;EACzD,+BAA+B;AACjC;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;AACzB;;AAEA,8CAA8C;AAC9C;EACE,yDAAyD;EACzD,+BAA+B;AACjC;;AAEA,qCAAqC,qBAAqB,EAAE;AAC5D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,uBAAuB,EAAE;AAC9D,qCAAqC,wBAAwB,EAAE;;AAE/D,oBAAoB;AACpB;EACE,+CAA+C;AACjD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,sBAAsB;AACtB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;AAC9B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,qBAAqB;AACvB;;AAEA;EACE,UAAU;EACV,mBAAmB;AACrB;;AAEA,kDAAkD;AAClD;EACE,yDAAyD;EACzD,kCAAkC;AACpC;;AAEA;EACE,sDAAsD;EACtD;;sCAEoC;AACtC;;AAEA,qCAAqC;AACrC;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,0EAA0E;EAC1E,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,gCAAgC;AAClC;;AAEA;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA,qBAAqB;AACrB;EACE,gBAAgB;EAChB,+BAA+B;EAC/B,mBAAmB;EACnB,0EAA0E;AAC5E;;AAEA;EACE;IACE,QAAQ;EACV;EACA;IACE,WAAW;EACb;AACF;;AAEA;EACE;IACE,yBAAyB;EAC3B;EACA;IACE,qBAAqB;EACvB;AACF;;AAEA,6BAA6B;AAC7B;EACE,yDAAyD;EACzD,sBAAsB;AACxB;;AAEA;EACE,uCAAuC;EACvC,uBAAuB;AACzB;;AAEA,8BAA8B;AAC9B;EACE,yDAAyD;EACzD,oDAAoD;AACtD;;AAEA;EACE,uCAAuC;EACvC,2CAA2C;AAC7C;;AAEA;EACE,oCAAoC;EACpC,yBAAyB;AAC3B;;AAEA,kBAAkB;AAClB;EACE,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;EAEE,wBAAwB;EACxB,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;AACd;;AAEA;EACE,iCAAiC;EACjC,cAAc;EACd,WAAW;AACb;;AAEA;EACE,iCAAiC;EACjC,cAAc;EACd,WAAW;AACb;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,+BAA+B;EACjC;EACA;IACE,8BAA8B;EAChC;EACA;IACE,gCAAgC;EAClC;EACA;IACE,+BAA+B;EACjC;AACF;;AAEA,gBAAgB;AAChB;EACE,2BAA2B;AAC7B;;AAEA;EACE,mBAAmB;AACrB;;AAEA,sBAAsB;AACtB;EACE,uBAAuB;EACvB,oDAAoD;EACpD,6DAA6D;AAC/D;;AAEA;EACE,uBAAuB;EACvB,oDAAoD;EACpD,6DAA6D;AAC/D;;AAEA;EACE,uBAAuB;EACvB,sDAAsD;EACtD,+DAA+D;AACjE;;AAEA,sBAAsB;AACtB;EACE,WAAW;AACb;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,SAAS;AACX;;AAEA,qBAAqB;AACrB;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,oBAAoB;AACtB;;AAEA;EACE,eAAe;EACf,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,qBAAqB;AACvB;;AAEA,uBAAuB;AACvB;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA,cAAc;AACd;EACE,YAAY;AACd;;AAEA;EACE,WAAW;AACb;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,QAAQ;AACV;;AAEA;EACE,WAAW;EACX,OAAO;AACT;;AAEA,sBAAsB;AACtB;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,gCAAgC;EAChC,mCAAmC;AACrC;;AAEA;EACE,0BAA0B;EAC1B,6BAA6B;EAC7B,+BAA+B;EAC/B,kCAAkC;AACpC;;AAEA,+FAA+F;;AA6F/F,oCAAoC;AACpC;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,wBAAwB;EAC1B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE;IACE,0BAA0B;IAC1B,gCAAgC;EAClC;EACA;IACE,0BAA0B;IAC1B,iCAAiC;EACnC;AACF;;AAEA;EACE;IACE,oBAAoB;EACtB;EACA;IACE,oBAAoB;EACtB;AACF;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,sCAAsC;AACxC;;AAEA,2BAA2B;AAC3B;EACE,uBAAuB;AACzB;;AAEA,4BAA4B;AAC5B;EACE;;gEAE8D;EAC9D,0BAA0B;AAC5B;;AAEA,uBAAuB;AACvB;EACE,6DAA6D;EAC7D,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,yBAAyB;AACzB;EACE,qCAAqC;EACrC,mCAA2B;UAA3B,2BAA2B;EAC3B,2CAA2C;AAC7C;;AAEA,qBAAqB;AACrB;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oDAAoD;EACpD,kBAAkB;AACpB;;AAEA;EACE,oDAAoD;AACtD;;AA9pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,mBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA,QA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA,sBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,iDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,kDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,4DA+pBA;EA/pBA,mEA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,gDA+pBA;EA/pBA,6DA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,iDA+pBA;EA/pBA,qDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,6EA+pBA;EA/pBA,iGA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,gFA+pBA;EA/pBA,oGA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,sBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,2GA+pBA;EA/pBA,yGA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,2GA+pBA;EA/pBA,yGA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,2GA+pBA;EA/pBA,yGA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,iBA+pBA;EA/pBA,iBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,+EA+pBA;EA/pBA,mGA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,yBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,yBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,yBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,gDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,iDA+pBA;EA/pBA;AA+pBA;;AA/pBA;;EAAA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;AAAA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;;EAAA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;AAAA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,0CA+pBA;EA/pBA,uDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA,yBA+pBA;EA/pBA,2BA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,+BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA,yBA+pBA;EA/pBA,2BA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,+BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA,yBA+pBA;EA/pBA,2BA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,+BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,wBA+pBA;EA/pBA,yBA+pBA;EA/pBA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,wBA+pBA;EA/pBA,yBA+pBA;EA/pBA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,wBA+pBA;EA/pBA,yBA+pBA;EA/pBA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA,yBA+pBA;EA/pBA,0BA+pBA;EA/pBA,wBA+pBA;EA/pBA,yBA+pBA;EA/pBA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA,sBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA,qBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,sBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,mBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,mBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,0EA+pBA;EA/pBA,8FA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,0BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;;EAAA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA,uBA+pBA;IA/pBA,sDA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,uBA+pBA;IA/pBA,oDA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,uBA+pBA;IA/pBA,2DA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA,qBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,oBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;AAAA;;AA/pBA;;EAAA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA,kBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,eA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,kBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,mBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,kBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA,0BA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA,0EA+pBA;IA/pBA,8FA+pBA;IA/pBA;EA+pBA;AAAA;;AA/pBA;;EAAA;IAAA;EA+pBA;;EA/pBA;IAAA;EA+pBA;;EA/pBA;IAAA,kBA+pBA;IA/pBA;EA+pBA;;EA/pBA;IAAA,iBA+pBA;IA/pBA;EA+pBA;AAAA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,iDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,kDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,kDA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,gBA+pBA;EA/pBA,uBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,gBA+pBA;EA/pBA,oBA+pBA;EA/pBA,4BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,sBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,eA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,WA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,mBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,8BA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,qBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,kBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA,oBA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA,WA+pBA;EA/pBA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA;;AA/pBA;EAAA;AA+pBA\",\"sourcesContent\":[\"\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\r\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');\\r\\n\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n/* Simple animations */\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n/* Language transition effects */\\r\\n[dir=\\\"rtl\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n[dir=\\\"ltr\\\"] {\\r\\n  transition: all 0.3s ease-in-out;\\r\\n}\\r\\n\\r\\n/* Text direction transitions */\\r\\n.text-transition {\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n/* Performance optimizations */\\r\\n* {\\r\\n  -webkit-font-smoothing: antialiased;\\r\\n  -moz-osx-font-smoothing: grayscale;\\r\\n}\\r\\n\\r\\n/* Reduce motion for accessibility */\\r\\n@media (prefers-reduced-motion: reduce) {\\r\\n  *,\\r\\n  *::before,\\r\\n  *::after {\\r\\n    animation-duration: 0.01ms !important;\\r\\n    animation-iteration-count: 1 !important;\\r\\n    transition-duration: 0.01ms !important;\\r\\n    scroll-behavior: auto !important;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n/* Text selection */\\r\\n::selection {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  color: inherit;\\r\\n}\\r\\n\\r\\n/* Focus styles */\\r\\n.focus-visible:focus {\\r\\n  outline: 2px solid #3b82f6;\\r\\n  outline-offset: 2px;\\r\\n}\\r\\n\\r\\n/* Line clamp utility */\\r\\n.line-clamp-3 {\\r\\n  display: -webkit-box;\\r\\n  -webkit-line-clamp: 3;\\r\\n  line-clamp: 3;\\r\\n  -webkit-box-orient: vertical;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Custom avatar glow effect */\\r\\n.avatar-glow {\\r\\n  box-shadow:\\r\\n    0 0 20px rgba(59, 130, 246, 0.3),\\r\\n    0 0 40px rgba(139, 92, 246, 0.2),\\r\\n    0 0 60px rgba(236, 72, 153, 0.1);\\r\\n}\\r\\n\\r\\n.avatar-glow:hover {\\r\\n  box-shadow:\\r\\n    0 0 30px rgba(59, 130, 246, 0.4),\\r\\n    0 0 60px rgba(139, 92, 246, 0.3),\\r\\n    0 0 90px rgba(236, 72, 153, 0.2);\\r\\n}\\r\\n\\r\\n/* Scroll Animation Classes - Enhanced for smoothness */\\r\\n.scroll-animate {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-100 {\\r\\n  transition-delay: 150ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-200 {\\r\\n  transition-delay: 300ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-300 {\\r\\n  transition-delay: 450ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-400 {\\r\\n  transition-delay: 600ms;\\r\\n}\\r\\n\\r\\n.scroll-animate-delay-500 {\\r\\n  transition-delay: 750ms;\\r\\n}\\r\\n\\r\\n/* Stagger animation for children - Enhanced */\\r\\n.stagger-children > * {\\r\\n  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, opacity;\\r\\n}\\r\\n\\r\\n.stagger-children > *:nth-child(1) { transition-delay: 0ms; }\\r\\n.stagger-children > *:nth-child(2) { transition-delay: 150ms; }\\r\\n.stagger-children > *:nth-child(3) { transition-delay: 300ms; }\\r\\n.stagger-children > *:nth-child(4) { transition-delay: 450ms; }\\r\\n.stagger-children > *:nth-child(5) { transition-delay: 600ms; }\\r\\n.stagger-children > *:nth-child(6) { transition-delay: 750ms; }\\r\\n.stagger-children > *:nth-child(7) { transition-delay: 900ms; }\\r\\n.stagger-children > *:nth-child(8) { transition-delay: 1050ms; }\\r\\n\\r\\n/* Parallax effect */\\r\\n.parallax-slow {\\r\\n  transform: translateY(var(--scroll-y, 0) * 0.5);\\r\\n}\\r\\n\\r\\n.parallax-fast {\\r\\n  transform: translateY(var(--scroll-y, 0) * -0.3);\\r\\n}\\r\\n\\r\\n/* Reveal animations */\\r\\n.reveal-up {\\r\\n  opacity: 0;\\r\\n  transform: translateY(50px);\\r\\n}\\r\\n\\r\\n.reveal-up.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.reveal-left {\\r\\n  opacity: 0;\\r\\n  transform: translateX(-50px);\\r\\n}\\r\\n\\r\\n.reveal-left.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-right {\\r\\n  opacity: 0;\\r\\n  transform: translateX(50px);\\r\\n}\\r\\n\\r\\n.reveal-right.revealed {\\r\\n  opacity: 1;\\r\\n  transform: translateX(0);\\r\\n}\\r\\n\\r\\n.reveal-scale {\\r\\n  opacity: 0;\\r\\n  transform: scale(0.8);\\r\\n}\\r\\n\\r\\n.reveal-scale.revealed {\\r\\n  opacity: 1;\\r\\n  transform: scale(1);\\r\\n}\\r\\n\\r\\n/* Hover effects for cards - Enhanced smoothness */\\r\\n.card-hover {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow;\\r\\n}\\r\\n\\r\\n.card-hover:hover {\\r\\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\\r\\n  box-shadow:\\r\\n    0 32px 64px -12px rgba(0, 0, 0, 0.25),\\r\\n    0 0 0 1px rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n/* Enhanced gradient text animation */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981, #f59e0b);\\r\\n  background-size: 400% 400%;\\r\\n  animation: gradient-shift 8s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  will-change: background-position;\\r\\n}\\r\\n\\r\\n@keyframes gradient-shift {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Typing animation */\\r\\n.typing-animation {\\r\\n  overflow: hidden;\\r\\n  border-right: 2px solid #3b82f6;\\r\\n  white-space: nowrap;\\r\\n  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;\\r\\n}\\r\\n\\r\\n@keyframes typing {\\r\\n  from {\\r\\n    width: 0;\\r\\n  }\\r\\n  to {\\r\\n    width: 100%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes blink-caret {\\r\\n  from, to {\\r\\n    border-color: transparent;\\r\\n  }\\r\\n  50% {\\r\\n    border-color: #3b82f6;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced magnetic effect */\\r\\n.magnetic {\\r\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform;\\r\\n}\\r\\n\\r\\n.magnetic:hover {\\r\\n  transform: scale(1.08) translateY(-2px);\\r\\n  filter: brightness(1.1);\\r\\n}\\r\\n\\r\\n/* Smooth button transitions */\\r\\n.btn-smooth {\\r\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\r\\n  will-change: transform, box-shadow, background-color;\\r\\n}\\r\\n\\r\\n.btn-smooth:hover {\\r\\n  transform: translateY(-2px) scale(1.02);\\r\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n\\r\\n.btn-smooth:active {\\r\\n  transform: translateY(0) scale(0.98);\\r\\n  transition-duration: 0.1s;\\r\\n}\\r\\n\\r\\n/* Glitch effect */\\r\\n.glitch {\\r\\n  position: relative;\\r\\n  animation: glitch 2s infinite;\\r\\n}\\r\\n\\r\\n.glitch::before,\\r\\n.glitch::after {\\r\\n  content: attr(data-text);\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.glitch::before {\\r\\n  animation: glitch-1 0.5s infinite;\\r\\n  color: #ff0000;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.glitch::after {\\r\\n  animation: glitch-2 0.5s infinite;\\r\\n  color: #00ff00;\\r\\n  z-index: -2;\\r\\n}\\r\\n\\r\\n@keyframes glitch {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-2px, 2px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-2px, -2px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(2px, 2px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(2px, -2px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-1 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes glitch-2 {\\r\\n  0%, 100% {\\r\\n    transform: translate(0);\\r\\n  }\\r\\n  20% {\\r\\n    transform: translate(1px, -1px);\\r\\n  }\\r\\n  40% {\\r\\n    transform: translate(1px, 1px);\\r\\n  }\\r\\n  60% {\\r\\n    transform: translate(-1px, -1px);\\r\\n  }\\r\\n  80% {\\r\\n    transform: translate(-1px, 1px);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* RTL Support */\\r\\n[dir=\\\"rtl\\\"] .flex-row {\\r\\n  flex-direction: row-reverse;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .flex-row-reverse {\\r\\n  flex-direction: row;\\r\\n}\\r\\n\\r\\n/* RTL Space between */\\r\\n[dir=\\\"rtl\\\"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\\r\\n  --tw-space-x-reverse: 1;\\r\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\r\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\r\\n}\\r\\n\\r\\n/* RTL Gap utilities */\\r\\n[dir=\\\"rtl\\\"] .gap-2 {\\r\\n  gap: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-3 {\\r\\n  gap: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .gap-4 {\\r\\n  gap: 1rem;\\r\\n}\\r\\n\\r\\n/* RTL Icon spacing */\\r\\n[dir=\\\"rtl\\\"] .mr-2 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-2 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .mr-3 {\\r\\n  margin-right: 0;\\r\\n  margin-left: 0.75rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .ml-3 {\\r\\n  margin-left: 0;\\r\\n  margin-right: 0.75rem;\\r\\n}\\r\\n\\r\\n/* RTL Text alignment */\\r\\n[dir=\\\"rtl\\\"] .text-left {\\r\\n  text-align: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .text-right {\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n/* RTL Float */\\r\\n[dir=\\\"rtl\\\"] .float-left {\\r\\n  float: right;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .float-right {\\r\\n  float: left;\\r\\n}\\r\\n\\r\\n/* RTL Positioning */\\r\\n[dir=\\\"rtl\\\"] .left-0 {\\r\\n  left: auto;\\r\\n  right: 0;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .right-0 {\\r\\n  right: auto;\\r\\n  left: 0;\\r\\n}\\r\\n\\r\\n/* RTL Border radius */\\r\\n[dir=\\\"rtl\\\"] .rounded-l {\\r\\n  border-top-left-radius: 0;\\r\\n  border-bottom-left-radius: 0;\\r\\n  border-top-right-radius: 0.25rem;\\r\\n  border-bottom-right-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n[dir=\\\"rtl\\\"] .rounded-r {\\r\\n  border-top-right-radius: 0;\\r\\n  border-bottom-right-radius: 0;\\r\\n  border-top-left-radius: 0.25rem;\\r\\n  border-bottom-left-radius: 0.25rem;\\r\\n}\\r\\n\\r\\n/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 222.2 84% 4.9%;\\r\\n\\r\\n    --primary: 222.2 47.4% 11.2%;\\r\\n    --primary-foreground: 210 40% 98%;\\r\\n\\r\\n    --secondary: 210 40% 96.1%;\\r\\n    --secondary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --muted: 210 40% 96.1%;\\r\\n    --muted-foreground: 215.4 16.3% 46.9%;\\r\\n\\r\\n    --accent: 210 40% 96.1%;\\r\\n    --accent-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 214.3 31.8% 91.4%;\\r\\n    --input: 214.3 31.8% 91.4%;\\r\\n    --ring: 222.2 84% 4.9%;\\r\\n\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    --sidebar-background: 0 0% 98%;\\r\\n    --sidebar-foreground: 240 5.3% 26.1%;\\r\\n    --sidebar-primary: 240 5.9% 10%;\\r\\n    --sidebar-primary-foreground: 0 0% 98%;\\r\\n    --sidebar-accent: 240 4.8% 95.9%;\\r\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\r\\n    --sidebar-border: 220 13% 91%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 222.2 84% 4.9%;\\r\\n    --foreground: 210 40% 98%;\\r\\n\\r\\n    --card: 222.2 84% 4.9%;\\r\\n    --card-foreground: 210 40% 98%;\\r\\n\\r\\n    --popover: 222.2 84% 4.9%;\\r\\n    --popover-foreground: 210 40% 98%;\\r\\n\\r\\n    --primary: 210 40% 98%;\\r\\n    --primary-foreground: 222.2 47.4% 11.2%;\\r\\n\\r\\n    --secondary: 217.2 32.6% 17.5%;\\r\\n    --secondary-foreground: 210 40% 98%;\\r\\n\\r\\n    --muted: 217.2 32.6% 17.5%;\\r\\n    --muted-foreground: 215 20.2% 65.1%;\\r\\n\\r\\n    --accent: 217.2 32.6% 17.5%;\\r\\n    --accent-foreground: 210 40% 98%;\\r\\n\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 210 40% 98%;\\r\\n\\r\\n    --border: 217.2 32.6% 17.5%;\\r\\n    --input: 217.2 32.6% 17.5%;\\r\\n    --ring: 212.7 26.8% 83.9%;\\r\\n    --sidebar-background: 240 5.9% 10%;\\r\\n    --sidebar-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-primary: 224.3 76.3% 48%;\\r\\n    --sidebar-primary-foreground: 0 0% 100%;\\r\\n    --sidebar-accent: 240 3.7% 15.9%;\\r\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\r\\n    --sidebar-border: 240 3.7% 15.9%;\\r\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Enhanced animations and effects */\\r\\n@keyframes fade-in {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(30px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-20px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes bounce-slow {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-25px);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse-slow {\\r\\n  0%, 100% {\\r\\n    opacity: 0.4;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.8;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes gradient-x {\\r\\n  0%, 100% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: left center;\\r\\n  }\\r\\n  50% {\\r\\n    background-size: 200% 200%;\\r\\n    background-position: right center;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes scale-x-100 {\\r\\n  from {\\r\\n    transform: scaleX(0);\\r\\n  }\\r\\n  to {\\r\\n    transform: scaleX(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-fade-in {\\r\\n  animation: fade-in 0.8s ease-out forwards;\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-bounce-slow {\\r\\n  animation: bounce-slow 3s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse-slow 4s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-gradient-x {\\r\\n  animation: gradient-x 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Smooth scroll behavior */\\r\\nhtml {\\r\\n  scroll-behavior: smooth;\\r\\n}\\r\\n\\r\\n/* Grid pattern background */\\r\\n.bg-grid-pattern {\\r\\n  background-image: \\r\\n    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\\r\\n    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n}\\r\\n\\r\\n/* Enhanced gradients */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Glassmorphism effect */\\r\\n.glass-effect {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  backdrop-filter: blur(10px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: #f1f1f1;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: linear-gradient(45deg, #667eea, #764ba2);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: linear-gradient(45deg, #5a6fd8, #6a419a);\\r\\n}\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/index.css\n"));

/***/ })

});